{"LOG": true, "API_TIMEOUT_MS": 600000, "Providers": [{"name": "openrouter", "api_base_url": "https://openrouter.ai/api/v1/chat/completions", "api_key": "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", "models": ["qwen/qwen3-coder:free", "qwen/qwen3-235b-a22b-2507:free", "qwen/qwen3-235b-a22b-thinking-2507", "moonshotai/kimi-k2:free", "z-ai/glm-4.5-air:free", "z-ai/glm-4.5-air", "z-ai/glm-4.5", "deepseek/deepseek-chat-v3-0324:free", "deepseek/deepseek-r1-0528:free", "google/gemini-2.5-flash", "google/gemini-2.5-pro"], "transformer": {"use": ["openrouter"]}}, {"name": "gemini", "api_base_url": "https://generativelanguage.googleapis.com/v1beta/models/", "api_key": "AIzaSyDvnVxe6YSZG1cC2MBmp3aOznId2BTNMbo", "models": ["gemini-2.5-flash", "gemini-2.5-pro"], "transformer": {"use": ["gemini"]}}, {"name": "modelscope", "api_base_url": "https://api-inference.modelscope.cn/v1/chat/completions", "api_key": "ms-2156d6fc-735e-47fb-8288-9e0beb6d07ad", "models": ["Qwen/Qwen3-Coder-480B-A35B-Instruct", "Qwen/Qwen3-235B-A22B-Thinking-2507", "Qwen/Qwen3-235B-A22B-Instruct-2507", "ZhipuAI/GLM-4.5", "moonshotai/Kimi-K2-Instruct"], "transformer": {"use": ["modelscope"]}}], "Router": {"default": "ZhipuAI/GLM-4.5", "background": "moonshotai/Kimi-K2-Instruct", "think": "gemini,gemini-2.5-pro", "longContext": "modelscope,moonshotai/Kimi-K2-Instruct", "longContextThreshold": 60000, "webSearch": "gemini,gemini-2.5-flash"}}